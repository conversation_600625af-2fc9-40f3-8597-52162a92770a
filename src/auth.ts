import { betterAuth } from "better-auth";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { google } from "better-auth/social-providers";
import { jwt } from "better-auth/plugins/jwt";
import { getNativeMongoDb } from "./lib/db.js";

// Create Better Auth configuration function using native MongoDB driver
export const createAuth = () => {
  const db = getNativeMongoDb();
  
  // Debug log the environment variables
  console.log("Google Client ID exists:", !!process.env.GOOGLE_CLIENT_ID);
  console.log("Google Client Secret exists:", !!process.env.GOOGLE_CLIENT_SECRET);
  
  return betterAuth({
    // Required environment variables
    secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-key",
    baseURL: process.env.BACKEND_BASE_URL || process.env.BETTER_AUTH_URL || "http://localhost:5000",
    basePath: "/api/auth",
    
    // Trusted origins for CORS
    trustedOrigins: [
      process.env.FRONTEND_BASE_URL || "http://localhost:3000", 
      process.env.BACKEND_BASE_URL || "http://localhost:5000"
    ],
    
    // MongoDB adapter - use native driver
    database: mongodbAdapter(db, {
      debugLogs: process.env.NODE_ENV === "development",
    }),
    
    // OAuth providers (replacing Clerk's Google OAuth)
    socialProviders: {
      google: {
        prompt: "select_account",
        clientId: process.env.GOOGLE_CLIENT_ID!,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      },
    },
    
    // JWT plugin for session management
    plugins: [
      jwt(),
    ],
    
    // Callbacks
    callbacks: {
      onSignIn: async ({ user, account }: { user: any; account: any }) => {
        // Handle sign in logic
        console.log("User signed in:", user.email);
        return true;
      },
      
      onUserCreate: async ({ user }: { user: any }) => {
        // Handle user creation logic
        console.log("User created:", user.email);
        return true;
      },
    },
    
    // Default redirect URL after successful authentication
    defaultRedirect: process.env.FRONTEND_BASE_URL || "http://localhost:3000",
    onAPIError: {
      errorURL: `${process.env.FRONTEND_BASE_URL || "http://localhost:3000"}/auth/error`
    }
  });
};

// Initialize auth after database connection
let auth: ReturnType<typeof createAuth> | null = null;

export const initializeAuth = () => {
  if (!auth) {
    auth = createAuth();
  }
  return auth;
};

export const getAuth = () => {
  if (!auth) {
    throw new Error("Auth not initialized. Call initializeAuth() first.");
  }
  return auth;
}; 