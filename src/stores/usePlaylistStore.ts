import { axiosInstance } from "@/lib/axios";
import { Playlist, PlaylistsResponse } from "@/types";
import toast from "react-hot-toast";
import { create } from "zustand";

interface PlaylistStore {
	// State
	userPlaylists: Playlist[];
	publicPlaylists: Playlist[];
	currentPlaylist: Playlist | null;
	isLoading: boolean;
	error: string | null;
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	} | null;

	// Actions
	fetchUserPlaylists: () => Promise<void>;
	fetchPublicPlaylists: (page?: number, limit?: number) => Promise<void>;
	fetchPlaylistById: (id: string) => Promise<void>;
	createPlaylist: (data: { name: string; description?: string; isPublic?: boolean }) => Promise<Playlist | null>;
	updatePlaylist: (id: string, data: { name?: string; description?: string; isPublic?: boolean }) => Promise<void>;
	deletePlaylist: (id: string) => Promise<void>;
	addSongToPlaylist: (playlistId: string, songId: string) => Promise<void>;
	removeSongFromPlaylist: (playlistId: string, songId: string) => Promise<void>;
	addCollaborator: (playlistId: string, userId: string) => Promise<void>;
	removeCollaborator: (playlistId: string, collaboratorId: string) => Promise<void>;

	// Utility functions
	getPlaylistById: (id: string) => Playlist | null;
	clearCurrentPlaylist: () => void;
	clearError: () => void;
}

export const usePlaylistStore = create<PlaylistStore>((set, get) => ({
	// Initial state
	userPlaylists: [],
	publicPlaylists: [],
	currentPlaylist: null,
	isLoading: false,
	error: null,
	pagination: null,

	// Fetch user's playlists
	fetchUserPlaylists: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<Playlist[]>("/playlists/user");
			set({ userPlaylists: response.data });
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to fetch playlists";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Fetch public playlists
	fetchPublicPlaylists: async (page = 1, limit = 20) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<PlaylistsResponse>(
				`/playlists/public?page=${page}&limit=${limit}`
			);
			set({ 
				publicPlaylists: response.data.playlists,
				pagination: response.data.pagination 
			});
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to fetch public playlists";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Fetch playlist by ID
	fetchPlaylistById: async (id: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<Playlist>(`/playlists/${id}`);
			set({ currentPlaylist: response.data });
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to fetch playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Create new playlist
	createPlaylist: async (data: { name: string; description?: string; isPublic?: boolean }) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.post<Playlist>("/playlists", data);
			const newPlaylist = response.data;
			
			// Add to user playlists
			set((state) => ({
				userPlaylists: [newPlaylist, ...state.userPlaylists]
			}));
			
			toast.success("Playlist created successfully");
			return newPlaylist;
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to create playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
			return null;
		} finally {
			set({ isLoading: false });
		}
	},

	// Update playlist
	updatePlaylist: async (id: string, data: { name?: string; description?: string; isPublic?: boolean }) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.put<Playlist>(`/playlists/${id}`, data);
			const updatedPlaylist = response.data;
			
			// Update in user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.map(playlist =>
					playlist._id === id ? updatedPlaylist : playlist
				),
				currentPlaylist: state.currentPlaylist?._id === id ? updatedPlaylist : state.currentPlaylist
			}));
			
			toast.success("Playlist updated successfully");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to update playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Delete playlist
	deletePlaylist: async (id: string) => {
		set({ isLoading: true, error: null });
		try {
			await axiosInstance.delete(`/playlists/${id}`);
			
			// Remove from user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.filter(playlist => playlist._id !== id),
				currentPlaylist: state.currentPlaylist?._id === id ? null : state.currentPlaylist
			}));
			
			toast.success("Playlist deleted successfully");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to delete playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Add song to playlist
	addSongToPlaylist: async (playlistId: string, songId: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.post<Playlist>(`/playlists/${playlistId}/songs`, { songId });
			const updatedPlaylist = response.data;
			
			// Update in user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.map(playlist =>
					playlist._id === playlistId ? updatedPlaylist : playlist
				),
				currentPlaylist: state.currentPlaylist?._id === playlistId ? updatedPlaylist : state.currentPlaylist
			}));
			
			toast.success("Song added to playlist");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to add song to playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Remove song from playlist
	removeSongFromPlaylist: async (playlistId: string, songId: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.delete<Playlist>(`/playlists/${playlistId}/songs/${songId}`);
			const updatedPlaylist = response.data;
			
			// Update in user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.map(playlist =>
					playlist._id === playlistId ? updatedPlaylist : playlist
				),
				currentPlaylist: state.currentPlaylist?._id === playlistId ? updatedPlaylist : state.currentPlaylist
			}));
			
			toast.success("Song removed from playlist");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to remove song from playlist";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Add collaborator to playlist
	addCollaborator: async (playlistId: string, userId: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.post<Playlist>(`/playlists/${playlistId}/collaborators`, { userId });
			const updatedPlaylist = response.data;
			
			// Update in user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.map(playlist =>
					playlist._id === playlistId ? updatedPlaylist : playlist
				),
				currentPlaylist: state.currentPlaylist?._id === playlistId ? updatedPlaylist : state.currentPlaylist
			}));
			
			toast.success("Collaborator added to playlist");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to add collaborator";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Remove collaborator from playlist
	removeCollaborator: async (playlistId: string, collaboratorId: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.delete<Playlist>(`/playlists/${playlistId}/collaborators/${collaboratorId}`);
			const updatedPlaylist = response.data;
			
			// Update in user playlists
			set((state) => ({
				userPlaylists: state.userPlaylists.map(playlist =>
					playlist._id === playlistId ? updatedPlaylist : playlist
				),
				currentPlaylist: state.currentPlaylist?._id === playlistId ? updatedPlaylist : state.currentPlaylist
			}));
			
			toast.success("Collaborator removed from playlist");
		} catch (error: any) {
			const errorMessage = error.response?.data?.message || "Failed to remove collaborator";
			set({ error: errorMessage });
			toast.error(errorMessage);
		} finally {
			set({ isLoading: false });
		}
	},

	// Utility functions
	getPlaylistById: (id: string) => {
		const { userPlaylists, publicPlaylists } = get();
		return [...userPlaylists, ...publicPlaylists].find(playlist => playlist._id === id) || null;
	},

	clearCurrentPlaylist: () => {
		set({ currentPlaylist: null });
	},

	clearError: () => {
		set({ error: null });
	},
}));
