import { useEffect } from "react";
import { useMusicStore } from "@/stores/useMusicStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { FollowButton } from "@/components/ui/FollowButton";
import { ChevronLeft, ChevronRight, Users } from "lucide-react";
import { useRef } from "react";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import { useEngagementStore } from "@/stores/useEngagementStore";

export const TabletPopularArtists = () => {
  const { popularArtists, fetchPopularArtists, isLoading } = useMusicStore();
   const {
	  getArtistFollowerCount,
	  getArtistLikeCount,
	  artistLikeCounts,
	  artistFollowerCounts,
	} = useEngagementStore();

  useEffect(() => {
    fetchPopularArtists();
  }, [fetchPopularArtists]);

  if (isLoading) {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4 px-6">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <Users className="h-5 w-5" />
            Popular Artists
          </h2>
        </div>
        <div className="flex gap-4 overflow-hidden px-6">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-40 animate-pulse">
              <div className="bg-zinc-800 rounded-lg p-4 text-center">
                <div className="w-18 h-18 bg-zinc-700 rounded-full mx-auto mb-3"></div>
                <div className="h-4 bg-zinc-700 rounded mb-2"></div>
                <div className="h-3 bg-zinc-700 rounded mb-3 w-3/4 mx-auto"></div>
                <div className="h-7 bg-zinc-700 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!popularArtists.length) {
    return null;
  }

  return (
    <div className="my-8 ml-6">
      <div className="flex items-center justify-between mb-4 mt-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          Popular Artists
        </h2>
      </div>

      <ScrollArea>
        <div
          // ref={scrollContainerRef}
          className="flex gap-2 overflow-x-auto scrollbar-hide pb-2"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {popularArtists.map((artist) => {
            const followerCount = getArtistFollowerCount(
              artist._id,
              artist.followerCount || 0
            );
            const likeCount = getArtistLikeCount(
              artist._id,
              artist.totalLikes || 0
            );

            return (
              <div className="flex-shrink-0 w-24 bg-white rounded-lg text-center cursor-pointer group">
                <Link to={`/artist/${artist._id}`} key={artist._id}>
                  <Avatar className="w-20 h-20 mx-auto mb-3">
                    <AvatarImage
                      src={artist.imageUrl}
                      alt={artist.name}
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <AvatarFallback className="bg-gradient-to-br from-green-400 to-blue-600 text-white text-lg font-bold">
                      {artist.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  <h3 className="text-gray-900 font-semibold mb-3 truncate group-hover:underline group-hover:text-primary">
                    {artist.name}
                  </h3>

                  <p
                    className="text-sm truncate"
                    style={{
                      color: "#86868b",
                      fontSize: "14px",
                      fontWeight: "400",
                      lineHeight: "1.4",
                    }}
                  >
                    {artistFollowerCounts.get(artist._id) || followerCount}{" "}
                    Followers
                  </p>
                  {/* <p
                    className="text-sm truncate"
                    style={{
                      color: "#86868b",
                      fontSize: "14px",
                      fontWeight: "400",
                      lineHeight: "1.4",
                    }}
                  >
                    {artistLikeCounts.get(artist._id) || likeCount} Likes
                  </p> */}
                </Link>
              </div>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
};
