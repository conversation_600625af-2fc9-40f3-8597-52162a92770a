import { useEffect } from "react";
import { useMusicStore } from "@/stores/useMusicStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { FollowButton } from "@/components/ui/FollowButton";
import { Users } from "lucide-react";
import { Link } from "react-router-dom";

export const MobilePopularArtists = () => {
  const { popularArtists, fetchPopularArtists, isLoading } = useMusicStore();
  const {
    getArtistFollowerCount,
    getArtistLikeCount,
    artistLikeCounts,
    artistFollowerCounts,
  } = useEngagementStore();

  useEffect(() => {
    fetchPopularArtists();
  }, [fetchPopularArtists]);

  if (isLoading) {
    return (
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4 px-4">
          <Users className="h-5 w-5 text-green-500" />
          <h2 className="text-lg font-bold text-white">Popular Artists</h2>
        </div>
        <div className="flex gap-3 overflow-hidden px-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-32 animate-pulse">
              <div className="bg-white rounded-lg p-3 text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-1"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!popularArtists.length) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex items-center gap-2 mb-4 px-4 mt-4">
        <h2 className="text-lg font-semibold">Popular Artists</h2>
      </div>

      <div className="flex gap-3 overflow-x-auto scrollbar-hide px-4 pb-2">
        {popularArtists.map((artist) => {
          const followerCount = getArtistFollowerCount(
            artist._id,
            artist.followerCount || 0
          );

          const likeCount = getArtistLikeCount(
            artist._id,
            artist.totalLikes || 0
          );

          return (
            <div className="flex-shrink-0 w-24 bg-white rounded-lg text-center cursor-pointer group">
              <Link to={`/artist/${artist._id}`} key={artist._id}>
                <Avatar className="w-20 h-20 mx-auto mb-3">
                  <AvatarImage
                    src={artist.imageUrl}
                    alt={artist.name}
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <AvatarFallback className="bg-gradient-to-br from-green-400 to-blue-600 text-white text-lg font-bold">
                    {artist.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <h3 className="text-gray-900 font-semibold mb-3 truncate group-hover:underline group-hover:text-primary">
                  {artist.name}
                </h3>

                <p
                  className="text-sm truncate"
                  style={{
                    color: "#86868b",
                    fontSize: "14px",
                    fontWeight: "400",
                    lineHeight: "1.4",
                  }}
                >
                  {artistFollowerCounts.get(artist._id) || followerCount} Followers
                </p>
                {/* <p
                  className="text-sm truncate"
                  style={{
                    color: "#86868b",
                    fontSize: "14px",
                    fontWeight: "400",
                    lineHeight: "1.4",
                  }}
                >
                  {artistLikeCounts.get(artist._id) || likeCount} Likes
                </p> */}
              </Link>
            </div>
          );
        })}
      </div>
    </div>
  );
};
