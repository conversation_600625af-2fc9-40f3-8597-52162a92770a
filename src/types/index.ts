export interface Song {
	_id: string;
	title: string;
	artist: string;
	albumId: string | null;
	artistId: string | null;
	featuredArtists?: string[];
	imageUrl: string;
	audioUrl: string;
	duration: number;
	releaseDate: string; // Add release date field
	credits?: {
		role: string;
		name: string;
		artistId?: string;
	}[];
	composer?: string;
	producer?: string;
	source?: string;
	// Engagement fields
	playCount?: number;
	likeCount?: number;
	lastPlayedAt?: string;
	createdAt: string;
	updatedAt: string;
}

export interface Album {
	_id: string;
	title: string;
	artist: string;
	imageUrl: string;
	releaseYear: number;
	genre: string;
	songs: Song[];
	artistId: string | null;
}

export interface Artist {
	_id: string;
	name: string;
	imageUrl: string;
	bgColor?: string;
	about?: string;
	monthlyListeners?: number;
	genres?: string[];
	socialLinks?: {
		spotify?: string;
		instagram?: string;
		twitter?: string;
		website?: string;
	};
	// Engagement fields
	totalPlays?: number;
	totalLikes?: number;
	followerCount?: number;
	lastPlayedAt?: string;
	createdAt: string;
	updatedAt: string;
}

export interface Stats {
	totalSongs: number;
	totalAlbums: number;
	totalUsers: number;
	totalArtists: number;
}

export interface Message {
	_id: string;
	senderId: string;
	receiverId: string;
	content: string;
	createdAt: string;
	updatedAt: string;
}

export interface User {
	_id: string;
	clerkId: string;
	fullName: string;
	imageUrl: string;
}

export interface Playlist {
	_id: string;
	name: string;
	description?: string;
	imageUrl?: string;
	userId: string | User;
	songs: Song[];
	isPublic: boolean;
	collaborators: User[];
	createdAt: string;
	updatedAt: string;
	isLikedSongs?: boolean; // Special flag for the liked songs playlist
}

export interface PlaylistsResponse {
	playlists: Playlist[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

// Engagement-related types
export interface UserLike {
	_id: string;
	userId: string;
	targetId: string;
	targetType: 'song' | 'artist';
	createdAt: string;
	updatedAt: string;
}

export interface UserFollow {
	_id: string;
	userId: string;
	artistId: string;
	createdAt: string;
	updatedAt: string;
}

export interface PlayHistory {
	_id: string;
	userId: string;
	songId: string;
	sessionId: string;
	playedAt: string;
	createdAt: string;
	updatedAt: string;
}

// API Response types
export interface EngagementResponse {
	message: string;
	isLiked?: boolean;
	isFollowing?: boolean;
	likeCount?: number;
	followerCount?: number;
	playCount?: number;
}

export interface UserEngagementStatus {
	likedSongs: string[];
	likedArtists: string[];
	followedArtists: string[];
}

// Extended song with play history info
export interface SongWithPlayHistory extends Song {
	playedAt?: string;
}
