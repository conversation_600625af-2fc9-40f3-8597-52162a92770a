import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { Playlist } from "@/types";
import { Plus, Music, Users, Lock } from "lucide-react";
import { cn } from "@/lib/utils";

interface PlaylistSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  songId: string;
  songTitle?: string;
}

export const PlaylistSelectionModal = ({ 
  isOpen, 
  onClose, 
  songId, 
  songTitle 
}: PlaylistSelectionModalProps) => {
  const {
    userPlaylists,
    isLoading,
    fetchUserPlaylists,
    createPlaylist,
    addSongToPlaylist,
  } = usePlaylistStore();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState("");
  const [newPlaylistDescription, setNewPlaylistDescription] = useState("");
  const [newPlaylistIsPublic, setNewPlaylistIsPublic] = useState(true);

  useEffect(() => {
    if (isOpen) {
      fetchUserPlaylists();
    }
  }, [isOpen, fetchUserPlaylists]);

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) return;

    const playlist = await createPlaylist({
      name: newPlaylistName,
      description: newPlaylistDescription,
      isPublic: newPlaylistIsPublic,
    });

    if (playlist) {
      // Add song to the newly created playlist
      await addSongToPlaylist(playlist._id, songId);
      onClose();
      resetForm();
    }
  };

  const handleAddToPlaylist = async (playlistId: string) => {
    await addSongToPlaylist(playlistId, songId);
    onClose();
  };

  const resetForm = () => {
    setNewPlaylistName("");
    setNewPlaylistDescription("");
    setNewPlaylistIsPublic(true);
    setShowCreateForm(false);
    // setSelectedPlaylist(null);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  const isPlaylistContainsSong = (playlist: Playlist) => {
    return playlist.songs.some(song => song._id === songId);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Music className="w-5 h-5" />
            Add to Playlist
          </DialogTitle>
          {songTitle && (
            <p className="text-sm text-gray-600">
              Adding "{songTitle}" to playlist
            </p>
          )}
        </DialogHeader>

        <div className="space-y-4">
          {!showCreateForm ? (
            <>
              {/* Create New Playlist Button */}
              <Button
                onClick={() => setShowCreateForm(true)}
                className="w-full justify-start gap-2"
                variant="outline"
              >
                <Plus className="w-4 h-4" />
                Create New Playlist
              </Button>

              {/* Existing Playlists */}
              <div className="space-y-2">
                <Label>Your Playlists</Label>
                <ScrollArea className="h-48">
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : userPlaylists.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Music className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>No playlists yet</p>
                      <p className="text-sm">Create your first playlist!</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {userPlaylists.map((playlist) => {
                        const containsSong = isPlaylistContainsSong(playlist);
                        return (
                          <div
                            key={playlist._id}
                            className={cn(
                              "flex items-center justify-between p-3 rounded-lg border transition-colors",
                              containsSong
                                ? "bg-green-50 border-green-200 cursor-not-allowed"
                                : "hover:bg-gray-50 cursor-pointer border-gray-200"
                            )}
                            onClick={() => !containsSong && handleAddToPlaylist(playlist._id)}
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                                <Music className="w-5 h-5 text-gray-500" />
                              </div>
                              <div>
                                <p className="font-medium">{playlist.name}</p>
                                <div className="flex items-center gap-2 text-sm text-gray-500">
                                  <span>{playlist.songs.length} songs</span>
                                  {!playlist.isPublic && (
                                    <Lock className="w-3 h-3" />
                                  )}
                                  {playlist.collaborators.length > 0 && (
                                    <Users className="w-3 h-3" />
                                  )}
                                </div>
                              </div>
                            </div>
                            {containsSong && (
                              <span className="text-sm font-medium text-green-600">
                                Added
                              </span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </>
          ) : (
            /* Create Playlist Form */
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="playlist-name">Playlist Name</Label>
                <Input
                  id="playlist-name"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  placeholder="Enter playlist name"
                  maxLength={100}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="playlist-description">Description (Optional)</Label>
                <Textarea
                  id="playlist-description"
                  value={newPlaylistDescription}
                  onChange={(e) => setNewPlaylistDescription(e.target.value)}
                  placeholder="Enter playlist description"
                  rows={3}
                  maxLength={500}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="playlist-public"
                  checked={newPlaylistIsPublic}
                  onCheckedChange={setNewPlaylistIsPublic}
                />
                <Label htmlFor="playlist-public">Make playlist public</Label>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {showCreateForm ? (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(false)}
                disabled={isLoading}
              >
                Back
              </Button>
              <Button
                onClick={handleCreatePlaylist}
                disabled={!newPlaylistName.trim() || isLoading}
              >
                {isLoading ? "Creating..." : "Create & Add"}
              </Button>
            </div>
          ) : (
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
