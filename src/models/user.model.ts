import mongoose, { Document, Schema } from "mongoose";

export interface IUser extends Document {
	fullName: string;
	imageUrl: string;
	email: string;
}

const userSchema = new Schema<IUser>(
	{
		fullName: {
			type: String,
			required: true,
		},
		imageUrl: {
			type: String,
			required: true,
		},
		email: {
			type: String,
			required: true,
			unique: true,
		},
	},
	{ timestamps: true } //  createdAt, updatedAt
);

export const User = mongoose.model<IUser>("User", userSchema);
