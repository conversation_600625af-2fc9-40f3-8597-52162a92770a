import { Route, Routes } from "react-router-dom";
import HomePage from "./pages/home/<USER>";
import AuthCallbackPage from "./pages/auth-callback/AuthCallbackPage";
import SignInPage from "./pages/sign-in/SignInPage";
import MainLayout from "./layout/MainLayout";
import ChatPage from "./pages/chat/ChatPage";
import AlbumPage from "./pages/album/AlbumPage";
import AdminPage from "./pages/admin/AdminPage";

import { Toaster } from "react-hot-toast";
import NotFoundPage from "./pages/404/NotFoundPage";
import SongPage from "./pages/song/songPage";
import ArtistPage from "./pages/artist/artistPage";
import PlaylistPage from "./pages/playlist/playlistPage";

function App() {
  return (
    <>
      <Routes>
        <Route path="/sign-in" element={<SignInPage />} />
        <Route path="/auth-callback" element={<AuthCallbackPage />} />
        <Route path="/admin" element={<AdminPage />} />

        <Route element={<MainLayout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/chat" element={<ChatPage />} />
          <Route path="/albums/:albumId" element={<AlbumPage />} />
          <Route path="/song/:songId" element={<SongPage />} />
          <Route path="/artist/:artistId" element={<ArtistPage />} />
          <Route path="/playlist/:playlistId" element={<PlaylistPage />} /> 
          <Route path="/search" element={<AlbumPage />} /> // This route is for
          search results, yet to do
          <Route path="/search/:query" element={<AlbumPage />} /> // This route
          is for search results with a query, yet to do
          <Route path="/library" element={<AlbumPage />} /> // This route is for library, yet to do
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
