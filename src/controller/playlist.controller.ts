import { Response } from "express";
import mongoose from "mongoose";
import { Playlist } from "../models/playlist.model";
import { Song } from "../models/song.model";
import { User } from "../models/user.model";
import { AuthenticatedRequest } from "../types/index";

// Get all playlists for a user
export const getUserPlaylists = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const userId = req.auth?.user.id;
		if (!userId) {
			return res.status(401).json({ error: "User not authenticated" });
		}

		const playlists = await Playlist.find({ userId })
			.populate("songs", "title artist imageUrl duration")
			.sort({ createdAt: -1 });

		res.json(playlists);
	} catch (error) {
		console.error("Error fetching user playlists:", error);
		res.status(500).json({ error: "Failed to fetch playlists" });
	}
};

// Get public playlists
export const getPublicPlaylists = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { page = 1, limit = 20 } = req.query;
		const skip = (Number(page) - 1) * Number(limit);

		const playlists = await Playlist.find({ isPublic: true })
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration")
			.sort({ createdAt: -1 })
			.skip(skip)
			.limit(Number(limit));

		const total = await Playlist.countDocuments({ isPublic: true });

		res.json({
			playlists,
			pagination: {
				page: Number(page),
				limit: Number(limit),
				total,
				totalPages: Math.ceil(total / Number(limit)),
			},
		});
	} catch (error) {
		console.error("Error fetching public playlists:", error);
		res.status(500).json({ error: "Failed to fetch public playlists" });
	}
};

// Get playlist by ID
export const getPlaylistById = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id)) {
			return res.status(400).json({ error: "Invalid playlist ID" });
		}

		const playlist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration audioUrl albumId artistId playCount likeCount")
			.populate("collaborators", "fullName imageUrl");

		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Check if user has access to private playlist
		if (!playlist.isPublic && playlist.userId.toString() !== userId && !(playlist.collaborators && playlist.collaborators.some(c => c._id.toString() === userId))) {
			return res.status(403).json({ error: "Access denied" });
		}

		res.json(playlist);
	} catch (error) {
		console.error("Error fetching playlist:", error);
		res.status(500).json({ error: "Failed to fetch playlist" });
	}
};

// Create new playlist
export const createPlaylist = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const userId = req.auth?.user.id;
		if (!userId) {
			return res.status(401).json({ error: "User not authenticated" });
		}

		const { name, description, isPublic = true } = req.body;

		if (!name || name.trim().length === 0) {
			return res.status(400).json({ error: "Playlist name is required" });
		}

		const playlist = new Playlist({
			name: name.trim(),
			description: description?.trim(),
			userId,
			isPublic,
			songs: [],
		});

		await playlist.save();

		const populatedPlaylist = await Playlist.findById(playlist._id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration");

		res.status(201).json(populatedPlaylist);
	} catch (error) {
		console.error("Error creating playlist:", error);
		res.status(500).json({ error: "Failed to create playlist" });
	}
};

// Update playlist
export const updatePlaylist = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id)) {
			return res.status(400).json({ error: "Invalid playlist ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Check if user owns the playlist or is a collaborator
		if (playlist.userId.toString() !== userId && !(playlist.collaborators && playlist.collaborators.includes(userId as any))) {
			return res.status(403).json({ error: "Access denied" });
		}

		const { name, description, isPublic } = req.body;

		if (name !== undefined) playlist.name = name.trim();
		if (description !== undefined) playlist.description = description?.trim();
		if (isPublic !== undefined) playlist.isPublic = isPublic;

		await playlist.save();

		const updatedPlaylist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration")
			.populate("collaborators", "fullName imageUrl");

		res.json(updatedPlaylist);
	} catch (error) {
		console.error("Error updating playlist:", error);
		res.status(500).json({ error: "Failed to update playlist" });
	}
};

// Delete playlist
export const deletePlaylist = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id)) {
			return res.status(400).json({ error: "Invalid playlist ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Only owner can delete playlist
		if (playlist.userId.toString() !== userId) {
			return res.status(403).json({ error: "Only playlist owner can delete" });
		}

		await Playlist.findByIdAndDelete(id);
		res.json({ message: "Playlist deleted successfully" });
	} catch (error) {
		console.error("Error deleting playlist:", error);
		res.status(500).json({ error: "Failed to delete playlist" });
	}
};

// Add song to playlist
export const addSongToPlaylist = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const { songId } = req.body;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(songId)) {
			return res.status(400).json({ error: "Invalid playlist or song ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Check if user has permission to add songs
		if (playlist.userId.toString() !== userId && !(playlist.collaborators && playlist.collaborators.includes(userId as any))) {
			return res.status(403).json({ error: "Access denied" });
		}

		// Check if song exists
		const song = await Song.findById(songId);
		if (!song) {
			return res.status(404).json({ error: "Song not found" });
		}

		// Check if song is already in playlist
		if (playlist.songs.includes(songId)) {
			return res.status(400).json({ error: "Song already in playlist" });
		}

		playlist.songs.push(songId);
		await playlist.save();

		const updatedPlaylist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration audioUrl albumId artistId playCount likeCount");

		res.json(updatedPlaylist);
	} catch (error) {
		console.error("Error adding song to playlist:", error);
		res.status(500).json({ error: "Failed to add song to playlist" });
	}
};

// Remove song from playlist
export const removeSongFromPlaylist = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id, songId } = req.params;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(songId)) {
			return res.status(400).json({ error: "Invalid playlist or song ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Check if user has permission to remove songs
		if (playlist.userId.toString() !== userId && !(playlist.collaborators && playlist.collaborators.includes(userId as any))) {
			return res.status(403).json({ error: "Access denied" });
		}

		// Remove song from playlist
		playlist.songs = playlist.songs.filter(song => song.toString() !== songId);
		await playlist.save();

		const updatedPlaylist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration audioUrl albumId artistId playCount likeCount");

		res.json(updatedPlaylist);
	} catch (error) {
		console.error("Error removing song from playlist:", error);
		res.status(500).json({ error: "Failed to remove song from playlist" });
	}
};

// Add collaborator to playlist
export const addCollaborator = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const { userId: collaboratorId } = req.body;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(collaboratorId)) {
			return res.status(400).json({ error: "Invalid playlist or user ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Only owner can add collaborators
		if (playlist.userId.toString() !== userId) {
			return res.status(403).json({ error: "Only playlist owner can add collaborators" });
		}

		// Check if user exists
		const collaborator = await User.findById(collaboratorId);
		if (!collaborator) {
			return res.status(404).json({ error: "User not found" });
		}

		// Check if user is already a collaborator
		if (playlist.collaborators && playlist.collaborators.includes(collaboratorId as any)) {
			return res.status(400).json({ error: "User is already a collaborator" });
		}

		if (!playlist.collaborators) {
			playlist.collaborators = [];
		}
		playlist.collaborators.push(collaboratorId as any);
		await playlist.save();

		const updatedPlaylist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration")
			.populate("collaborators", "fullName imageUrl");

		res.json(updatedPlaylist);
	} catch (error) {
		console.error("Error adding collaborator:", error);
		res.status(500).json({ error: "Failed to add collaborator" });
	}
};

// Remove collaborator from playlist
export const removeCollaborator = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id, collaboratorId } = req.params;
		const userId = req.auth?.user.id;

		if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(collaboratorId)) {
			return res.status(400).json({ error: "Invalid playlist or user ID" });
		}

		const playlist = await Playlist.findById(id);
		if (!playlist) {
			return res.status(404).json({ error: "Playlist not found" });
		}

		// Only owner can remove collaborators
		if (playlist.userId.toString() !== userId) {
			return res.status(403).json({ error: "Only playlist owner can remove collaborators" });
		}

		// Remove collaborator
		if (playlist.collaborators) {
			playlist.collaborators = playlist.collaborators.filter(c => c.toString() !== collaboratorId);
		}
		await playlist.save();

		const updatedPlaylist = await Playlist.findById(id)
			.populate("userId", "fullName imageUrl")
			.populate("songs", "title artist imageUrl duration")
			.populate("collaborators", "fullName imageUrl");

		res.json(updatedPlaylist);
	} catch (error) {
		console.error("Error removing collaborator:", error);
		res.status(500).json({ error: "Failed to remove collaborator" });
	}
};
