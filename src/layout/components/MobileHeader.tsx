import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import MobileNavigationDrawer from "./MobileNavigationDrawer";
import { Link } from "react-router-dom";

interface MobileHeaderProps {
  title?: string;
  greeting?: string;
}

const MobileHeader = ({ title, greeting }: MobileHeaderProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const displayTitle = title || greeting || getGreeting();

  return (
    <>
      <div className="fixed top-0 left-0 right-0 z-40 bg-white/98 backdrop-blur-xl shadow-md border-b border-gray-200/50">
        {/* Safe area for status bar */}
        <div className="h-safe-area-inset-top bg-white/95"></div>

        <div className="flex items-center justify-between px-4 py-4">
          {/* Left side - Greeting/Title */}
          {/* <div className="flex-1 pr-4">
            <h1 
              className="text-2xl font-bold text-gray-900 truncate"
              style={{
                fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
                fontWeight: "700",
                lineHeight: "1.2"
              }}
            >
              {displayTitle}
            </h1>
          </div> */}
          <Link to="/" className="flex items-center gap-2 ml-4">
            <img
              src="/logo-withoutbd-notscaled.png"
              className="w-24 h-16"
              alt="SLM logo"
            />
          </Link>

          {/* Right side - Hamburger Menu */}
          <Button
            size="icon"
            variant="ghost"
            className="h-10 w-10 rounded-full text-gray-700 hover:text-gray-900 hover:bg-gray-100 active:scale-95 transition-all duration-150 mobile-button haptic-light"
            onClick={() => setIsDrawerOpen(true)}
            style={{ WebkitTapHighlightColor: "transparent" }}
          >
            <Menu className="h-8 w-8" strokeWidth={2} />
          </Button>
        </div>
      </div>

      {/* Navigation Drawer */}
      <MobileNavigationDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
      />
    </>
  );
};

export default MobileHeader;
