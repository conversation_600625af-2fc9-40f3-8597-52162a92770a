import PlaylistSkeleton from "@/components/skeletons/PlaylistSkeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useMusicStore } from "@/stores/useMusicStore";
import { useSession } from "@/lib/auth-client";
import { HomeIcon, Library, ListMusic, MessageCircle, Heart, Plus } from "lucide-react";
import { useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CreatePlaylistDialog } from "@/components/CreatePlaylistDialog";
import { PlaylistOptionsMenu } from "@/components/PlaylistOptionsMenu";

interface LeftSidebarProps {
  isCollapsed?: boolean;
}

const LeftSidebar = ({ isCollapsed = false }: LeftSidebarProps) => {
  const { albums, fetchAlbums, isLoading } = useMusicStore();
  const {
    userPlaylists,
    likedSongsPlaylist,
    fetchUserPlaylists,
    fetchLikedSongsPlaylist,
    createPlaylist,
    addSongToPlaylist,
  } = usePlaylistStore();
  const { fetchUserLikedSongs } = useEngagementStore();
  const { data: session } = useSession();
  const location = useLocation();

  useEffect(() => {
    // Fetch user playlists and liked songs on component mount
    if (session?.user) {
      fetchUserPlaylists();
      fetchLikedSongsPlaylist();
      fetchUserLikedSongs();
    }
    fetchAlbums();
  }, [fetchAlbums, fetchUserPlaylists, fetchLikedSongsPlaylist, fetchUserLikedSongs, session?.user]);

  const isActive = (path: string) => location.pathname === path;

  return (
    <div
      className="h-full flex flex-col gap-6 p-6"
      style={{ backgroundColor: "#ffffff" }}

    >
      {/* Navigation menu */}
      <div>
        {/* Brand Section */}
        <div className="mb-8">
          <div className="flex items-center gap-2">
            <Link to="/">
              <img
                src="/logo-withoutbd-notscaled.png"
                className="w-24 h-16"
                alt="SLM logo"
              />
            </Link>
          </div>
        </div>

        {/* Primary Navigation */}
        <div className="mb-8">
          <h3
            className="text-sm font-medium mb-4 px-2 hidden md:block"
            style={{
              color: "#86868b",
              fontSize: "14px",
              fontWeight: "500",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            Browse Music
          </h3>
          <div className="space-y-2">
            <Link
              to="/"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                isActive("/")
                  ? "text-white shadow-lg"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              style={{
                backgroundColor: isActive("/") ? "#D9AD39" : "transparent",
                fontSize: "16px",
                fontWeight: isActive("/") ? "600" : "400",
              }}
            >
              <HomeIcon className="w-5 h-5" />
              <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                Home
              </span>
            </Link>

            <Link
              to="/library"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                isActive("/library")
                  ? "text-white shadow-lg"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              style={{
                backgroundColor: isActive("/library")
                  ? "#D9AD39"
                  : "transparent",
                fontSize: "16px",
                fontWeight: isActive("/library") ? "600" : "400",
              }}
            >
              <Library className="w-5 h-5" />
              <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                Library
              </span>
            </Link>

            {/* {session?.user && (
              <Link
                to="/chat"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                  isActive("/chat")
                    ? "text-white shadow-lg"
                    : "text-gray-700 hover:bg-gray-100"
                )}
                style={{
                  backgroundColor: isActive("/chat")
                    ? "#D9AD39"
                    : "transparent",
                  fontSize: "16px",
                  fontWeight: isActive("/chat") ? "600" : "400",
                }}
              >
                <MessageCircle className="w-5 h-5" />
                <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                  Messages
                </span>
              </Link>
            )} */}
          </div>
        </div>
      </div>

      {/* Library section */}
      <div className="flex-1">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4 px-2">
            <h3
              className="text-sm font-medium hidden md:block"
              style={{
                color: "#86868b",
                fontSize: "14px",
                fontWeight: "500",
                textTransform: "uppercase",
                letterSpacing: "0.5px",
              }}
            >
              Library
            </h3>
            {session?.user && (
              <CreatePlaylistDialog>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100 hidden md:flex"
                  title="Create playlist"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </CreatePlaylistDialog>
            )}
          </div>

          {/* Create Playlist Button for mobile */}
          {session?.user && (
            <div className="mb-3 px-3 md:hidden">
              <CreatePlaylistDialog />
            </div>
          )}
        </div>

        <ScrollArea className="h-[calc(100vh-400px)]">
          <div className="space-y-2">
            {isLoading ? (
              <PlaylistSkeleton />
            ) : (
              <>
                {/* Liked Songs - Always show first if user is logged in */}
                {session?.user && likedSongsPlaylist && (
                  <Link
                    to="/liked"
                    className={cn(
                      "flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] group cursor-pointer",
                      isActive("/liked")
                        ? "text-white shadow-lg"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                    style={{
                      backgroundColor: isActive("/liked")
                        ? "#D9AD39"
                        : "transparent",
                    }}
                  >
                    <div className="relative">
                      <div className="w-12 h-12 rounded-md bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                        <Heart className="w-6 h-6 text-white fill-current" />
                      </div>
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200" />
                    </div>

                    <div className="flex-1 min-w-0 hidden md:block">
                      <p
                        className="font-medium truncate"
                        style={{
                          fontSize: "16px",
                          fontWeight: "600",
                        }}
                      >
                        Liked Songs
                      </p>
                      <p
                        className="text-sm truncate opacity-70"
                        style={{
                          fontSize: "14px",
                          fontWeight: "400",
                        }}
                      >
                        {likedSongsPlaylist.songs.length} liked songs
                      </p>
                    </div>
                  </Link>
                )}

                {/* User Playlists */}
                {userPlaylists.map((playlist) => (
                  <div key={playlist._id} className="group relative">
                    <Link
                      to={`/playlist/${playlist._id}`}
                      className={cn(
                        "flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] cursor-pointer",
                        isActive(`/playlist/${playlist._id}`)
                          ? "text-white shadow-lg"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                      style={{
                        backgroundColor: isActive(`/playlist/${playlist._id}`)
                          ? "#D9AD39"
                          : "transparent",
                      }}
                    >
                      <div className="relative">
                        <Avatar className="rounded-md w-12 h-12">
                          <AvatarImage
                            src={playlist.imageUrl || playlist.songs[0]?.imageUrl}
                            alt={playlist.name}
                            className="object-cover"
                          />
                          <AvatarFallback className="bg-gray-200 text-gray-600">
                            <ListMusic className="w-5 h-5" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200" />
                      </div>

                      <div className="flex-1 min-w-0 hidden md:block">
                        <p
                          className="font-medium truncate"
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                          }}
                        >
                          {playlist.name}
                        </p>
                        <p
                          className="text-sm truncate opacity-70"
                          style={{
                            fontSize: "14px",
                            fontWeight: "400",
                          }}
                        >
                          Playlist • {playlist.songs.length} songs
                        </p>
                      </div>
                    </Link>

                    {/* Options Menu */}
                    <div className="absolute right-2 top-1/2 -translate-y-1/2">
                      <PlaylistOptionsMenu playlist={playlist} />
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
export default LeftSidebar;
