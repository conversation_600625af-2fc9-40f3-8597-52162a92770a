import PlaylistSkeleton from "@/components/skeletons/PlaylistSkeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { useMusicStore } from "@/stores/useMusicStore";
import { useSession } from "@/lib/auth-client";
import { HomeIcon, Library, ListMusic, MessageCircle } from "lucide-react";
import { useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface LeftSidebarProps {
  isCollapsed?: boolean;
}

const LeftSidebar = ({ isCollapsed = false }: LeftSidebarProps) => {
  const { albums, fetchAlbums, isLoading } = useMusicStore();
  const {
    userPlaylists,
    fetchUserPlaylists,
    createPlaylist,
    addSongToPlaylist,
  } = usePlaylistStore();
  const { data: session } = useSession();
  const location = useLocation();

  useEffect(() => {
    // Fetch user playlists on component mount
    if (session?.user) {
      fetchUserPlaylists();
    }
    fetchAlbums();
  }, [fetchAlbums]);

  const isActive = (path: string) => location.pathname === path;

  return (
    <div
      className="h-full flex flex-col gap-6 p-6"
      style={{ backgroundColor: "#ffffff" }}

    >
      {/* Navigation menu */}
      <div>
        {/* Brand Section */}
        <div className="mb-8">
          <div className="flex items-center gap-2">
            <Link to="/">
              <img
                src="/logo-withoutbd-notscaled.png"
                className="w-24 h-16"
                alt="SLM logo"
              />
            </Link>
          </div>
        </div>

        {/* Primary Navigation */}
        <div className="mb-8">
          <h3
            className="text-sm font-medium mb-4 px-2 hidden md:block"
            style={{
              color: "#86868b",
              fontSize: "14px",
              fontWeight: "500",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            Browse Music
          </h3>
          <div className="space-y-2">
            <Link
              to="/"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                isActive("/")
                  ? "text-white shadow-lg"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              style={{
                backgroundColor: isActive("/") ? "#D9AD39" : "transparent",
                fontSize: "16px",
                fontWeight: isActive("/") ? "600" : "400",
              }}
            >
              <HomeIcon className="w-5 h-5" />
              <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                Home
              </span>
            </Link>

            <Link
              to="/library"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                isActive("/library")
                  ? "text-white shadow-lg"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              style={{
                backgroundColor: isActive("/library")
                  ? "#D9AD39"
                  : "transparent",
                fontSize: "16px",
                fontWeight: isActive("/library") ? "600" : "400",
              }}
            >
              <Library className="w-5 h-5" />
              <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                Library
              </span>
            </Link>

            {/* {session?.user && (
              <Link
                to="/chat"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                  isActive("/chat")
                    ? "text-white shadow-lg"
                    : "text-gray-700 hover:bg-gray-100"
                )}
                style={{
                  backgroundColor: isActive("/chat")
                    ? "#D9AD39"
                    : "transparent",
                  fontSize: "16px",
                  fontWeight: isActive("/chat") ? "600" : "400",
                }}
              >
                <MessageCircle className="w-5 h-5" />
                <span className={isCollapsed ? "hidden" : "hidden md:inline"}>
                  Messages
                </span>
              </Link>
            )} */}
          </div>
        </div>
      </div>

      {/* Library section */}
      <div className="flex-1">
        <div className="mb-6">
          <h3
            className="text-sm font-medium mb-4 px-2 hidden md:block"
            style={{
              color: "#86868b",
              fontSize: "14px",
              fontWeight: "500",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            Library
          </h3>
          <div className="flex items-center gap-3 px-3 py-2 text-gray-700">
            <ListMusic className="w-5 h-5" />
            <span
              className={
                isCollapsed ? "hidden" : "hidden md:inline font-medium"
              }
            >
              Your Playlists
            </span>
          </div>
        </div>

        <ScrollArea className="h-[calc(100vh-400px)]">
          <div className="space-y-3">
            {isLoading ? (
              <PlaylistSkeleton />
            ) : (
              userPlaylists.map((playlist) => (
                <Link
                  to={`/playlist/${playlist._id}`}
                  key={playlist._id}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] group cursor-pointer",
                    isActive(`/playlists/${playlist._id}`)
                      ? "text-white shadow-lg"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                  style={{
                    backgroundColor: isActive(`/playlist/${playlist._id}`)
                      ? "#D9AD39"
                      : "transparent",
                  }}
                >
                  <div className="relative">
                    <Avatar className="rounded-md w-12 h-12">
                      <AvatarImage
                        src={playlist.imageUrl || playlist.songs[0]?.imageUrl}
                        alt={playlist.name}
                        className="object-cover"
                      />
                      <AvatarFallback>
                        {playlist.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200" />
                  </div>

                  <div className="flex-1 min-w-0 hidden md:block">
                    <p
                      className="font-medium truncate"
                      style={{
                        fontSize: "16px",
                        fontWeight: "600",
                      }}
                    >
                      {playlist.name}
                    </p>
                    <p
                      className="text-sm truncate opacity-70"
                      style={{
                        fontSize: "14px",
                        fontWeight: "400",
                      }}
                    >
                      Playlist • {playlist.songs.length} songs
                    </p>
                  </div>
                </Link>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
export default LeftSidebar;
