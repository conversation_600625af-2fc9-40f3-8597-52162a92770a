import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  X,
  Search,
  User,
  Settings,
  LayoutDashboard,
  Bell,
  Users,
  Download,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Heart,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSession, signOut } from "@/lib/auth-client";
import { useAuthStore } from "@/stores/useAuthStore";
import { usePlayerStore } from "@/stores/usePlayerStore";

interface MobileNavigationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNavigationDrawer = ({ isOpen, onClose }: MobileNavigationDrawerProps) => {
  const navigate = useNavigate();
  const { data: session } = useSession();
  const { isAdmin } = useAuthStore();
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } = usePlayerStore();
  const [searchQuery, setSearchQuery] = useState("");

  const handleNavigation = (path: string) => {
    navigate(path);
    onClose();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      onClose();
    }
  };

  const handleSignOut = () => {
    signOut();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-[100] transform transition-transform duration-300 ease-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
          <Button
            size="icon"
            variant="ghost"
            onClick={onClose}
            className="h-8 w-8 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex flex-col h-full overflow-y-auto">
          {/* Primary Section */}
          <div className="p-4 space-y-4">
            {/* Enhanced Search */}
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search songs, artists, albums..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-full border-gray-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </form>

            {/* Navigation Buttons */}
            <div className="flex items-center gap-2 mb-4">
              <Button
                onClick={() => navigate(-1)}
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full hover:bg-gray-100"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => navigate(1)}
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full hover:bg-gray-100"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* User Profile */}
            {session?.user && (
              <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                {session.user.image ? (
                  <img
                    src={session.user.image}
                    alt="Profile"
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="w-5 h-5 text-primary" />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-gray-900 truncate">
                    {session.user.name || session.user.email}
                  </p>
                  <p className="text-sm text-gray-500">View profile</p>
                </div>
              </div>
            )}

            {/* Admin Dashboard */}
            {isAdmin && (
              <Button
                onClick={() => handleNavigation("/admin")}
                className="w-full justify-start gap-3 h-12 bg-primary text-white hover:bg-primary/90"
              >
                <LayoutDashboard className="w-5 h-5" />
                Admin Dashboard
              </Button>
            )}
          </div>

          {/* Secondary Section */}
          <div className="px-4 py-2 border-t border-gray-100">
            <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wide">
              Activity
            </h3>
            <div className="space-y-2">
              {/* <Button
                onClick={() => handleNavigation("/notifications")}
                variant="ghost"
                className="w-full justify-start gap-3 h-10"
              >
                <Bell className="w-4 h-4" />
                Notifications
                <Badge variant="secondary" className="ml-auto bg-red-100 text-red-600">
                  3
                </Badge>
              </Button>
              
              <Button
                onClick={() => handleNavigation("/friends")}
                variant="ghost"
                className="w-full justify-start gap-3 h-10"
              >
                <Users className="w-4 h-4" />
                Friend Activity
                <Badge variant="secondary" className="ml-auto bg-green-100 text-green-600">
                  5
                </Badge>
              </Button> */}

              <Button
                onClick={() => handleNavigation("/download")}
                variant="ghost"
                className="w-full justify-start gap-3 h-10"
              >
                <Download className="w-4 h-4" />
                Install App
              </Button>
            </div>
          </div>

          {/* Player Section */}
          {currentSong && (
            <div className="px-4 py-4 border-t border-gray-100 mt-auto">
              <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wide">
                Now Playing
              </h3>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <img
                    src={currentSong.imageUrl}
                    alt={currentSong.title}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{currentSong.title}</p>
                    <p className="text-sm text-gray-500 truncate">{currentSong.artist}</p>
                  </div>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>

                {/* Playback Controls */}
                <div className="flex items-center justify-center gap-4">
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={playPrevious}
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <SkipBack className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    size="icon"
                    onClick={togglePlay}
                    className="h-10 w-10 rounded-full bg-primary text-white hover:bg-primary/90"
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5 ml-0.5" />
                    )}
                  </Button>
                  
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={playNext}
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <SkipForward className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Settings & Sign Out */}
          <div className="px-4 py-4 border-t border-gray-100 space-y-2">
            <Button
              onClick={() => handleNavigation("/settings")}
              variant="ghost"
              className="w-full justify-start gap-3 h-10"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>

            {session?.user && (
              <Button
                onClick={handleSignOut}
                variant="ghost"
                className="w-full justify-start gap-3 h-10 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="w-4 h-4" />
                Sign Out
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileNavigationDrawer;